using Azure;
using Azure.AI.OpenAI;
using Azure.AI.OpenAI.Chat;
using Microsoft.Extensions.Configuration;
using OpenAI;
using OpenAI.Chat;
using System.ClientModel;

namespace CometSqlOpenAi.Service {
    public class OpenAIService {
        private readonly AzureOpenAIClient _openAiClient;
        private readonly ChatClient _chatClient;
        private readonly string _deploymentName;
        private readonly BlobStorageService _blobStorageService;

        public OpenAIService(IConfiguration configuration, BlobStorageService blobStorageService) {
            var endpoint = new Uri(configuration["AzureOpenAI:Endpoint"]);
            var apiKey = new ApiKeyCredential(configuration["AzureOpenAI:ApiKey"]);
            _openAiClient = new AzureOpenAIClient(endpoint, apiKey);
            _deploymentName = configuration["AzureOpenAI:DeploymentName"];
            _chatClient = _openAiClient.GetChatClient(_deploymentName);
            _blobStorageService = blobStorageService;

            if (string.IsNullOrEmpty(_deploymentName))
                throw new ArgumentException("Deployment name is not configured");
        }

        public async Task<string> CallAzureOpenAI(string prompt) {
            try {
                var response = await _chatClient.CompleteChatAsync(new UserChatMessage(prompt));
                var chatCompletion = response?.Value?.Content?.FirstOrDefault()?.Text;
                return chatCompletion ?? string.Empty;
            } catch (RequestFailedException ex) {
                throw new HttpRequestException($"Azure OpenAI API request failed: {ex.Message}", ex);
            }
        }
        
        public async Task<string> CallAzureOpenAIWithStoredPrompt(string promptName, Dictionary<string, string> replacements = null) {
            try {
                // Get the prompt template from blob storage
                string promptTemplate = await _blobStorageService.GetPromptAsync(promptName);
                
                // Replace placeholders with actual values
                if (replacements != null) {
                    foreach (var replacement in replacements) {
                        promptTemplate = promptTemplate.Replace($"{{{{{replacement.Key}}}}}", replacement.Value);
                    }
                }
                
                // Call OpenAI with the processed prompt
                return await CallAzureOpenAI(promptTemplate);
            } catch (Exception ex) {
                throw new Exception($"Failed to process prompt '{promptName}': {ex.Message}", ex);
            }
        }
    }
}