{"IsEncrypted": false, "Values": {"AzureWebJobsStorage": "UseDevelopmentStorage=true", "FUNCTIONS_WORKER_RUNTIME": "dotnet-isolated", "AzureOpenAI:Endpoint": "https://your-openai-resource.openai.azure.com/", "AzureOpenAI:ApiKey": "your-api-key", "AzureOpenAI:DeploymentName": "your-deployment-name", "BlobStorage:ConnectionString": "your-blob-storage-connection-string", "BlobStorage:ContainerName": "content"}, "ConnectionStrings": {"SqlConnectionString": "Server=your-synapse-workspace-name.sql.azuresynapse.net;Database=your-database-name;Authentication=Active Directory Default;"}, "DataLake": {"StorageAccountName": "your-datalake-storage-account", "ContainerName": "your-container-name", "ConnectionString": "your-datalake-connection-string"}}