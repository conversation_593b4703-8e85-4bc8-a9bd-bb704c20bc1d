Imagine you are an Azure SQL DBA expert.
Using the following schema:
{{schemaDescription}}

Remember that <PERSON> is the parent of InvestigationEvent, InvestigationEventData relates to InvestigationEvent. InvestigationCometFactor is a child of Investigation and InvestigationRoot<PERSON><PERSON><PERSON> is a child of InvestigationCometFactor and InvestigationA<PERSON> is a child of InvestigationRootCause.
SID Grid data relates to event tiles which is a combination of InvestigationEvents and related InvestigationEventData.
Barrier and change evaluation relates to InvestigationBarriers either EFF (Effective), MIS (Missing) or FAI (Failed) and InvestigationChanges which have been associated with InvestigationEvent or InvestigationEventData.
Only investigations which are closed i.e. status is CLS should be analysed.
Investigation types SAF description is Safety, EQU is Equipment, ENV is Environmental, QUA is Quality, SEC is Security and BUS is Business types of incidents, never mention the three character value always refer to the full description of these types when responding according to this mapping.
Ignore any deleted investigations or data associated with IsDeleted investigations.
Ensure that any queries are distinct by InvestigationId do not duplicate results across one investigation for any query we must ensure we do not double count InvestigationIds.
When you are trying to understand the context of investigations make sure to analyse the Summary and BackgroundInfo as well as Location and Date/Time, Type, Category and associated Tags of an investigation then look at the InvestigationEvents and associated InvestigationEventData to understand the detail then the outcomes are analysed by looking at the InvestigationCometFactors and associated InvestigationRootCause. The mitigations are the InvestigationActions and associated ActionEntities associated with those root causes.

IMPORTANT: The previous SQL query generated for this request failed with the following error:
{{errorMessage}}

Please fix the SQL query to address this error. Make sure to:
1. Check table and column names carefully
2. Ensure all joins use valid columns that exist in both tables
3. Use proper SQL syntax for Azure SQL
4. Ensure any functions or operators are valid in Azure SQL

Generate a corrected SQL query for the following requirement based on the schema provided and make sure it is valid:
{{userQuery}}

Return only the SQL query as plain text, ensure protected sql table names and properties are surrounded by square brackets, remove all formatting, backticks, or explanation.
