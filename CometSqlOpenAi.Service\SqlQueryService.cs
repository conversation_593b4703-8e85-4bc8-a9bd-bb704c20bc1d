using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using System.Text;
using System.Collections.Generic;

namespace CometSqlOpenAi.Service {
    public class SqlQueryService {
        private readonly string _connectionString;
        private readonly BlobStorageService _blobStorageService;

        public SqlQueryService(IConfiguration configuration, BlobStorageService blobStorageService) {
            _connectionString = configuration.GetConnectionString("SqlConnectionString");
            _blobStorageService = blobStorageService;
        }

        public async Task<string> ExecuteQueryAsync(string sqlQuery) {
            var result = new StringBuilder();

            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            using var command = new SqlCommand(sqlQuery, connection);
            using var reader = await command.ExecuteReaderAsync();

            while (await reader.ReadAsync()) {
                for (int i = 0; i < reader.FieldCount; i++) {
                    result.AppendLine($"{reader.GetName(i)}: {reader.GetValue(i)}");
                }
                result.AppendLine("---");
            }

            return result.ToString();
        }
        
        public async Task<string> ExecuteStoredQueryAsync(string queryName, Dictionary<string, object>? parameters = null)
        {
            // Get the SQL query from blob storage
            string sqlQuery = await _blobStorageService.GetSqlQueryAsync(queryName);
            
            var result = new StringBuilder();

            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            using var command = new SqlCommand(sqlQuery, connection);
            
            // Add parameters if provided
            if (parameters != null)
            {
                foreach (var param in parameters)
                {
                    command.Parameters.AddWithValue(param.Key, param.Value ?? DBNull.Value);
                }
            }
            
            using var reader = await command.ExecuteReaderAsync();

            while (await reader.ReadAsync()) {
                for (int i = 0; i < reader.FieldCount; i++) {
                    result.AppendLine($"{reader.GetName(i)}: {reader.GetValue(i)}");
                }
                result.AppendLine("---");
            }

            return result.ToString();
        }
    }
}
