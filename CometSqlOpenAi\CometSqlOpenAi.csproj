﻿<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<AzureFunctionsVersion>v4</AzureFunctionsVersion>
		<OutputType>Exe</OutputType>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<FrameworkReference Include="Microsoft.AspNetCore.App" />

		<!-- Azure Functions Worker Packages -->
		<PackageReference Include="Microsoft.Azure.Functions.Worker" Version="2.0.0" />
		<PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.Http" Version="3.3.0" />
		<PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.Http.AspNetCore" Version="2.0.1" />
		<PackageReference Include="Microsoft.Azure.Functions.Worker.Sdk" Version="2.0.0" PrivateAssets="All" />

		<!-- Dependency Injection and Configuration -->
		<PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.1" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.1" />				
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\CometSqlOpenAi.Service\CometSqlOpenAi.Service.csproj" />
	</ItemGroup>

	<ItemGroup>
		<None Update="host.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="local.settings.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
			<CopyToPublishDirectory>Never</CopyToPublishDirectory>
		</None>
	</ItemGroup>
</Project>
