using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CometSqlOpenAi.Service {
    public class DatabaseSchemaService {
        private readonly string _connectionString;        
        private readonly BlobStorageService _blobStorageService;

        public DatabaseSchemaService(IConfiguration configuration, BlobStorageService blobStorageService) {
            _connectionString = configuration.GetConnectionString("SqlConnectionString");
            _blobStorageService = blobStorageService;
        }

        public async Task<List<DatabaseSchema>> ExtractAllSchemas() {
            var schemaList = new List<DatabaseSchema>();

            // Get the schema extraction query from blob storage
            string sqlQuery = await _blobStorageService.GetSqlQueryAsync("ExtractSchemas");

            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            using var command = new SqlCommand(sqlQuery, connection);            

            using var reader = await command.ExecuteReaderAsync();
            while (reader.Read()) {
                schemaList.Add(new DatabaseSchema {
                    SchemaName = reader["TABLE_SCHEMA"].ToString(),
                    TableName = reader["TABLE_NAME"].ToString(),
                    ColumnName = reader["COLUMN_NAME"].ToString(),
                    DataType = reader["DATA_TYPE"].ToString(),
                    TableComment = reader["TableComment"].ToString()
                });
            }

            return schemaList;
        }      
    }

    public class DatabaseSchema {
        public string SchemaName { get; set; } = string.Empty;
        public string TableName { get; set; } = string.Empty;
        public string ColumnName { get; set; } = string.Empty;
        public string DataType { get; set; } = string.Empty;
        public string TableComment { get; set; } = string.Empty;
    }
}
