using CometSqlOpenAi.Service;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using System.Text.Json;

public class RequestFunction {
    private readonly OpenAIService _openAiService;
    private readonly SqlQueryService _sqlQueryService;
    private readonly DatabaseSchemaService _schemaService;
    private readonly BlobStorageService _blobStorageService;
    private readonly ILogger<RequestFunction> _logger;

    public RequestFunction(
        OpenAIService openAiService,
        SqlQueryService sqlQueryService,
        DatabaseSchemaService schemaService,
        BlobStorageService blobStorageService,
        ILoggerFactory loggerFactory) {

        _openAiService = openAiService;
        _sqlQueryService = sqlQueryService;
        _schemaService = schemaService;
        _blobStorageService = blobStorageService;
        _logger = loggerFactory.CreateLogger<RequestFunction>();
    }

    [Function("Request")]
    public async Task<HttpResponseData> RunAsync([HttpTrigger(AuthorizationLevel.Function, "post")] HttpRequestData req) {
        _logger.LogInformation("Request function processing a request");
        
        try {
            var requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            var data = JsonSerializer.Deserialize<Dictionary<string, string>>(requestBody);
            var userQuery = data?["query"];

            if (string.IsNullOrEmpty(userQuery)) {
                _logger.LogWarning("Request received with missing query parameter");
                var badRequestResponse = req.CreateResponse(System.Net.HttpStatusCode.BadRequest);
                await badRequestResponse.WriteStringAsync("Query parameter is missing.");
                return badRequestResponse;
            }

            _logger.LogInformation("Processing query: {Query}", userQuery);

            try {
                // Step 1: Extract the schema
                var schemaData = await _schemaService.ExtractAllSchemas();
                _logger.LogInformation("Schema extracted successfully");

                // Step 2: Format the schema into a prompt context for OpenAI
                var schemaDescription = FormatSchemaForPrompt(schemaData);

                // Step 3: Generate SQL query using OpenAI
                var replacements = new Dictionary<string, string>
                {
                    { "schemaDescription", schemaDescription },
                    { "userQuery", userQuery }
                };
                
                _logger.LogInformation("Calling OpenAI to generate SQL query");
                var sqlQuery = await _openAiService.CallAzureOpenAIWithStoredPrompt("SqlGeneration", replacements);
                sqlQuery = sqlQuery.Replace("`", "").Trim();
                sqlQuery = sqlQuery.Replace("sql", "").Trim();
                _logger.LogInformation("SQL query generated: {SqlQuery}", sqlQuery);

                // Step 4: Execute the generated SQL query
                _logger.LogInformation("Executing SQL query");
                string sqlResult;
                try {
                    sqlResult = await _sqlQueryService.ExecuteQueryAsync(sqlQuery);
                    _logger.LogInformation("SQL query executed successfully");
                } catch (Exception sqlEx) {
                    _logger.LogError(sqlEx, "Error executing SQL query");
                    
                    // Try to get a better SQL query by informing OpenAI about the error
                    var errorReplacements = new Dictionary<string, string>
                    {
                        { "schemaDescription", schemaDescription },
                        { "userQuery", userQuery },
                        { "errorMessage", sqlEx.Message }
                    };
                    
                    _logger.LogInformation("Attempting to generate a corrected SQL query");
                    sqlQuery = await _openAiService.CallAzureOpenAIWithStoredPrompt("SqlGenerationWithError", errorReplacements);
                    sqlQuery = sqlQuery.Replace("`", "").Trim();
                    sqlQuery = sqlQuery.Replace("sql", "").Trim();
                    
                    try {
                        sqlResult = await _sqlQueryService.ExecuteQueryAsync(sqlQuery);
                        _logger.LogInformation("Corrected SQL query executed successfully");
                    } catch (Exception retryEx) {
                        _logger.LogError(retryEx, "Error executing corrected SQL query");
                        var errorResponse = req.CreateResponse(System.Net.HttpStatusCode.OK);
                        await errorResponse.WriteStringAsync($"I'm sorry, but I couldn't generate a valid SQL query for your request. The database reported: {retryEx.Message}. Please try rephrasing your question or providing more details.");
                        return errorResponse;
                    }
                }

                // Step 5: Analyze SQL results and respond in plain language
                var analysisReplacements = new Dictionary<string, string>
                {
                    { "userQuery", userQuery },
                    { "sqlQuery", sqlQuery },
                    { "sqlResult", sqlResult }
                };
                
                _logger.LogInformation("Analyzing SQL results with OpenAI");
                var userFriendlyResponse = await _openAiService.CallAzureOpenAIWithStoredPrompt("ResultAnalysis", analysisReplacements);
                _logger.LogInformation("Analysis complete, returning response to user");

                // Step 6: Return the response to the user
                var response = req.CreateResponse(System.Net.HttpStatusCode.OK);
                await response.WriteStringAsync(userFriendlyResponse);
                return response;
            } catch (FileNotFoundException fnfEx) {
                _logger.LogError(fnfEx, "Required template file not found");
                var errorResponse = req.CreateResponse(System.Net.HttpStatusCode.OK);
                await errorResponse.WriteStringAsync("I'm sorry, but I'm missing some configuration files needed to process your request. Please contact the administrator.");
                return errorResponse;
            } catch (Exception ex) {
                _logger.LogError(ex, "Error processing query");
                var errorResponse = req.CreateResponse(System.Net.HttpStatusCode.OK);
                await errorResponse.WriteStringAsync("I'm sorry, but I encountered an error while processing your request. Please try again later or contact support if the issue persists.");
                return errorResponse;
            }
        } catch (JsonException jsonEx) {
            _logger.LogError(jsonEx, "Invalid JSON in request body");
            var errorResponse = req.CreateResponse(System.Net.HttpStatusCode.BadRequest);
            await errorResponse.WriteStringAsync("Invalid request format. Please provide a valid JSON with a 'query' field.");
            return errorResponse;
        } catch (Exception ex) {
            _logger.LogError(ex, "Unhandled exception in Request function");
            var errorResponse = req.CreateResponse(System.Net.HttpStatusCode.OK);
            await errorResponse.WriteStringAsync("I'm sorry, but something went wrong. Please try again later.");
            return errorResponse;
        }
    }

    private string FormatSchemaForPrompt(IEnumerable<DatabaseSchema> schemaData) {
        // Convert schema data to a human-readable string format for the OpenAI prompt
        var schemaDescription = new System.Text.StringBuilder();

        foreach (var group in schemaData.GroupBy(s => s.TableName)) {
            var table = group.Key;
            var tableComment = group.FirstOrDefault()?.TableComment ?? string.Empty;

            schemaDescription.AppendLine($"Table: {table}");
            if (!string.IsNullOrEmpty(tableComment)) {
                schemaDescription.AppendLine($"  Description: {tableComment}");
            }
            foreach (var column in group) {
                schemaDescription.AppendLine($"    - {column.ColumnName} ({column.DataType})");
            }
            schemaDescription.AppendLine();
        }

        return schemaDescription.ToString();
    }
}
