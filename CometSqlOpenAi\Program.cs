using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.DependencyInjection;
using CometSqlOpenAi.Service;
using Microsoft.Extensions.Logging;

var host = new HostBuilder()
    .ConfigureFunctionsWebApplication()
    .ConfigureLogging(logging =>
    {
        logging.AddConsole();
        logging.SetMinimumLevel(LogLevel.Information);
    })
    .ConfigureServices((context, services) => {
        // Add custom services to the DI container
        services.AddTransient<BlobStorageService>();
        services.AddTransient<DatabaseSchemaService>();        
        services.AddTransient<SqlQueryService>();
        services.AddTransient<OpenAIService>();
    })
    .Build();

host.Run();
