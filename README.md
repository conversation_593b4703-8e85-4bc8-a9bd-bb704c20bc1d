# COMET SQL OpenAI

COMET SQL OpenAI is a service that uses Azure OpenAI to generate and execute SQL queries based on natural language requests.

## Configuration

The application requires the following configuration settings in the `local.settings.json` file:

```json
{
  "IsEncrypted": false,
  "Values": {
    "AzureWebJobsStorage": "UseDevelopmentStorage=true",
    "FUNCTIONS_WORKER_RUNTIME": "dotnet-isolated",
    "AzureOpenAI:Endpoint": "https://your-openai-resource.openai.azure.com/",
    "AzureOpenAI:ApiKey": "your-api-key",
    "AzureOpenAI:DeploymentName": "your-deployment-name",
    "BlobStorage:ConnectionString": "your-blob-storage-connection-string",
    "BlobStorage:ContainerName": "content"
  },
  "ConnectionStrings": {
    "SqlConnectionString": "your-sql-connection-string"
  }
}
```

## Blob Storage Structure

The application uses Azure Blob Storage to store SQL queries and prompt templates. The following structure is expected:

```
/
├── sql/
│   └── ExtractSchemas.sql
│   └── (other SQL queries)
└── prompts/
    └── SqlGeneration.txt
    └── ResultAnalysis.txt
    └── (other prompt templates)
```

### SQL Queries

SQL queries are stored in the `sql` directory with a `.sql` extension. These queries can be parameterized and are executed by the `SqlQueryService`.

### Prompt Templates

Prompt templates are stored in the `prompts` directory with a `.txt` extension. These templates can include placeholders in the format `{{placeholderName}}` which will be replaced with actual values at runtime.

## Usage

Send a POST request to the `/api/Request` endpoint with a JSON body containing a `query` field:

```json
{
  "query": "How many safety incidents occurred in 2023?"
}
```

The service will:
1. Extract database schema information
2. Generate a SQL query using OpenAI
3. Execute the SQL query
4. Analyze the results using OpenAI
5. Return a user-friendly response
