using Azure.Storage.Blobs;
using Microsoft.Extensions.Configuration;
using System.Text;

namespace CometSqlOpenAi.Service {
    public class BlobStorageService {
        private readonly BlobServiceClient _blobServiceClient;
        private readonly string? _containerName;

        public BlobStorageService(IConfiguration configuration) {
            var connectionString = configuration["BlobStorage:ConnectionString"];
            _containerName = configuration["BlobStorage:ContainerName"];

            if (connectionString != null) {
                _blobServiceClient = new BlobServiceClient(connectionString);
            } else {
                throw new ArgumentNullException(nameof(connectionString), "Blob storage connection string is not configured");
            }

            if (string.IsNullOrEmpty(_containerName)) {
                throw new ArgumentNullException(nameof(_containerName), "Blob storage container name is not configured");
            }
        }

        public async Task<string> GetTextFromBlobAsync(string blobName) {
            if (_containerName == null) {
                throw new InvalidOperationException("Container name is not configured");
            }

            var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
            var blobClient = containerClient.GetBlobClient(blobName);

            if (!await blobClient.ExistsAsync()) {
                throw new FileNotFoundException($"Blob '{blobName}' not found in container '{_containerName}'");
            }

            var response = await blobClient.DownloadContentAsync();
            return response.Value.Content.ToString();
        }

        public async Task<string> GetSqlQueryAsync(string queryName) {
            return await GetTextFromBlobAsync($"sql/{queryName}.sql");
        }

        public async Task<string> GetPromptAsync(string promptName) {
            return await GetTextFromBlobAsync($"prompts/{promptName}.txt");
        }
    }
}
